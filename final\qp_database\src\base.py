import os
import re
import json
import glob
import time
import shutil
import logging
import pandas as pd
import google.generativeai as genai
from dotenv import load_dotenv
import fitz  # PyMuPDF
import cv2
import tempfile
from datetime import datetime
from pathlib import Path
from ultralytics import YOLO
from openai import OpenAI
import yaml
from openpyxl import Workbook
from openpyxl.styles import Alignment, Font
from openpyxl.utils.dataframe import dataframe_to_rows
import base64

# Load environment variables
load_dotenv()

class PDFProcessor:
    def __init__(self, input_dir, output_dir, image_dir):
        # Convert to absolute paths
        base_dir = Path(__file__).parent.parent
        self.input_dir = str(base_dir / input_dir)
        self.output_dir = str(base_dir / output_dir)
        self.image_dir = str(base_dir / image_dir)

        # Create required directories
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.image_dir, exist_ok=True)

        # Initialize Gemini API
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("GEMINI_API_KEY not found in environment variables")

        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel("gemini-2.0-flash")
        self.response_counter = 1

        # Initialize OpenAI client for YOLO functionality
        self.openai_client = OpenAI(
            api_key=os.getenv("GEMINI_API_KEY"),
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )

        # Track processed images to avoid duplicates - enhanced tracking
        self.processed_image_signatures = {}
        self.question_image_counter = {}
        self.global_image_signatures = set()  # Track all processed image signatures globally

    def compute_image_signature(self, image_array):
        """Compute enhanced signature of cropped image to detect exact duplicates."""
        try:
            import hashlib
            # Convert to grayscale and resize for comparison
            gray = cv2.cvtColor(image_array, cv2.COLOR_BGR2GRAY)
            resized = cv2.resize(gray, (64, 64))  # Increased resolution for better detection
            
            # Compute multiple features for better duplicate detection
            pixel_hash = hashlib.md5(resized.tobytes()).hexdigest()
            
            # Add histogram-based signature
            hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
            hist_hash = hashlib.md5(hist.tobytes()).hexdigest()
            
            # Combine signatures
            combined_signature = f"{pixel_hash}_{hist_hash}"
            return combined_signature
        except Exception as e:
            logging.error(f"Error computing signature: {e}")
            return None

    def is_duplicate_crop(self, image_array, question_id):
        """Enhanced duplicate detection with global tracking."""
        signature = self.compute_image_signature(image_array)
        if signature is None:
            return False
        
        # Check global duplicates first
        if signature in self.global_image_signatures:
            logging.info(f"Global duplicate detected for signature: {signature[:16]}...")
            return True
        
        # Create unique key for question + signature
        key = f"{question_id}_{signature}"
        
        if key in self.processed_image_signatures:
            return True
        
        # Mark both question-specific and global
        self.processed_image_signatures[key] = True
        self.global_image_signatures.add(signature)
        return False

    def validate_image_content(self, image_path, box_coords):
        """Enhanced validation to distinguish between actual images and mathematical content."""
        try:
            img = cv2.imread(image_path)
            x1, y1, x2, y2 = box_coords
            cropped = img[y1:y2, x1:x2]
            
            # Check if image is too small or empty
            if cropped.size == 0 or cropped.shape[0] < 20 or cropped.shape[1] < 20:
                return False
            
            # Save temporary cropped image
            temp_path = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False)
            temp_path.close()
            cv2.imwrite(temp_path.name, cropped)
            
            with open(temp_path.name, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            prompt = """
            Analyze this cropped region VERY STRICTLY to categorize content:
            
            Return "VISUAL" ONLY if it contains:
            - Actual photographs, illustrations, drawings
            - Real diagrams (flowcharts, circuit diagrams, anatomical drawings)
            - Charts, graphs with plotted data points
            - Maps, geometric shapes with measurements
            - Visual option choices (actual pictures as A, B, C, D)
            
            Return "MATHEMATICAL" if it contains:
            - Mathematical equations, formulas, expressions
            - Matrices, determinants, vectors
            - Mathematical symbols and notation
            - Fractions, integrals, summations, limits
            - Chemical formulas and equations
            - Mathematical graphs, functions, coordinate systems
            - Geometric theorems and proofs with symbols
            
            Return "TEXT" if it contains:
            - Plain text in any language (English, Tamil, etc.)
            - Option letters (A), (B), (C), (D) with text only
            - Paragraphs, sentences, word problems
            - Tables with only text content
            - Handwritten text or notes
            - Empty or mostly blank areas
            
            Be EXTREMELY strict: Mathematical content should be extracted as LaTeX, not treated as images.
            Only return "VISUAL" for actual photographs, illustrations, or real diagrams.
            
            Respond with EXACTLY ONE WORD: VISUAL, MATHEMATICAL, or TEXT
            """

            response = self.openai_client.chat.completions.create(
                model="gemini-1.5-flash",
                messages=[
                    {"role": "system", "content": "You are an expert at distinguishing visual content from mathematical content and text."},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ],
                max_tokens=5
            )
            
            os.remove(temp_path.name)
            result = response.choices[0].message.content.strip().upper()
            return result == "VISUAL"  # Only true visual content should be processed as images
            
        except Exception as e:
            logging.error(f"Error validating content: {e}")
            return False

    def identify_and_classify_images(self, image_path, boxes):
        """Enhanced classification for Physics question paper with better image detection."""
        try:
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            box_list_str = "\n".join([f"Box {i+1}: coordinates {box}" for i, box in enumerate(boxes)])
            prompt = f"""
            Analyze this Physics question paper page and classify each bounding box for VISUAL CONTENT ONLY.

            CRITICAL REQUIREMENTS FOR PHYSICS QUESTIONS:
            1. Questions are numbered sequentially (1, 2, 3, etc.)
            2. ONLY classify boxes containing ACTUAL VISUAL DIAGRAMS:
               - Circuit diagrams, mechanical systems, optical setups
               - Graphs with plotted data points, wave diagrams
               - Physical apparatus illustrations, experimental setups
               - Geometric figures with measurements or labels
            3. EXCLUDE mathematical content:
               - Equations, formulas, mathematical expressions
               - Pure text blocks with mathematical symbols
               - Mathematical graphs without visual context
            4. EXCLUDE logos, headers, footers, page numbers
            5. Each visual should be classified only once per question

            For each valid VISUAL box, determine:
            
            A) IMAGE TYPE:
            - "main_diagram": Primary physics diagram (circuit, apparatus, wave, etc.)
            - "figure": Additional physics illustrations or graphs with visual data

            B) QUESTION MAPPING:
            - Exact question number (1, 2, 3, etc.)
            - Language: "English" (this appears to be an English Physics paper)

            C) PHYSICS-SPECIFIC VALIDATION:
            - INCLUDE: Circuit diagrams, experimental setups, wave patterns, optical diagrams
            - INCLUDE: Graphs with plotted data points, mechanical systems, apparatus drawings
            - EXCLUDE: Pure mathematical equations, formulas, text blocks
            - EXCLUDE: Mathematical expressions without visual context
            - EXCLUDE: Logos, headers, footers, page numbers

            RESPONSE FORMAT (one line per valid visual box):
            Box <number>: <image_type> - Q<question_number>_English

            EXAMPLES:
            Box 2: main_diagram - Q1_English  (for a circuit diagram or experimental setup)
            Box 4: figure - Q2_English        (for a graph or additional illustration)

            Be STRICT: Only classify actual visual diagrams that help understand the physics concept.
            Mathematical formulas should be handled as LaTeX text, not images.
            If no valid VISUAL diagrams exist, respond with "No visual content"

            Detected boxes:
            {box_list_str}
            """

            response = self.openai_client.chat.completions.create(
                model="gemini-1.5-flash",
                messages=[
                    {"role": "system", "content": "You are an expert at identifying true visual content while excluding mathematical formulas and equations."},
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"}}
                        ]
                    }
                ],
                max_tokens=400
            )
            return response.choices[0].message.content
        except Exception as e:
            logging.error(f"Error classifying images: {e}")
            return ""

    def parse_image_classification(self, response):
        """Parse the image classification response with exact format and detailed logging."""
        valid_images = []

        if "no visual content" in response.lower():
            print("ℹ️  No visual content detected by classifier")
            return valid_images

        print(f"🔍 Parsing classification response: {response}")
        lines = response.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # Match: Box <number>: <type> - Q<number>_<language>
            match = re.search(r"Box\s+(\d+):\s*([^-]+?)\s*-\s*Q?(\d+)_(\w+)", line, re.IGNORECASE)
            if match:
                box_idx = int(match.group(1)) - 1  # Convert to 0-based
                image_type = match.group(2).strip().lower()
                q_number = match.group(3)
                language = match.group(4)

                # Validate image type
                valid_types = ['main_diagram', 'figure', 'option_a', 'option_b', 'option_c', 'option_d']
                if image_type in valid_types:
                    valid_images.append((box_idx, image_type, q_number, language))
                    print(f"✅ Parsed: Box {box_idx + 1} -> {image_type} for Q{q_number}")
                else:
                    print(f"⚠️  Invalid image type '{image_type}' in line: {line}")
            else:
                print(f"❌ Could not parse line: {line}")

        print(f"📊 Successfully parsed {len(valid_images)} image classifications")
        return valid_images

    def generate_correct_filename(self, image_type, q_number, language, page_num, pdf_name):
        """Generate correct filename based on PDF name and image type."""
        # Extract clean PDF name without extension
        clean_pdf_name = os.path.splitext(os.path.basename(pdf_name))[0]
        # Remove any special characters and replace with underscore
        clean_pdf_name = re.sub(r'[^a-zA-Z0-9_]', '_', clean_pdf_name)
        
        if image_type == "main_diagram":
            return f"{clean_pdf_name}_Q{q_number}_{language}_diagram_page{page_num}.jpg"
        elif image_type.startswith("option_"):
            option_letter = image_type.split("_")[1].upper()  # A, B, C, D
            return f"{clean_pdf_name}_Q{q_number}_{language}_option{option_letter}_page{page_num}.jpg"
        elif image_type == "figure":
            return f"{clean_pdf_name}_Q{q_number}_{language}_figure_page{page_num}.jpg"
        else:
            clean_type = re.sub(r'[^a-zA-Z0-9_]', '_', image_type)
            return f"{clean_pdf_name}_Q{q_number}_{language}_{clean_type}_page{page_num}.jpg"

    def process_and_save_classified_images(self, image_path, boxes, classified_images, page_num, pdf_name):
        """Process and save images with PDF name-based filenames."""
        saved_images = []
        
        if not classified_images:
            return saved_images

        img = cv2.imread(image_path)
        
        for box_idx, image_type, q_number, language in classified_images:
            try:
                if box_idx >= len(boxes):
                    logging.info(f"Invalid box index {box_idx}, skipping")
                    continue
                
                x1, y1, x2, y2 = boxes[box_idx]
                
                # Validate coordinates
                if (x1 >= x2 or y1 >= y2 or x1 < 0 or y1 < 0 or 
                    x2 > img.shape[1] or y2 > img.shape[0]):
                    logging.info(f"Invalid coordinates for box {box_idx}, skipping")
                    continue
                
                # Skip validation for now to ensure images are saved
                # TODO: Re-enable after fixing validation method
                # if not self.validate_image_content(image_path, (x1, y1, x2, y2)):
                #     logging.info(f"Box {box_idx+1} contains text content, skipping")
                #     continue
                print(f"💾 Saving image for Q{q_number}: Box {box_idx+1} ({x1},{y1},{x2},{y2})")
                
                # Crop image
                cropped = img[y1:y2, x1:x2]
                
                # Enhanced duplicate check with global tracking
                question_id = f"Q{q_number}_{language}_{image_type}"
                if self.is_duplicate_crop(cropped, question_id):
                    logging.info(f"Duplicate crop detected for {question_id}, skipping")
                    continue
                
                # Generate correct filename with PDF name
                filename = self.generate_correct_filename(image_type, q_number, language, page_num, pdf_name)
                image_save_path = os.path.join(self.image_dir, filename)
                
                # Save image
                cv2.imwrite(image_save_path, cropped)
                
                saved_images.append({
                    'path': image_save_path,
                    'filename': filename,
                    'type': image_type,
                    'question': q_number,
                    'language': language,
                    'box_idx': box_idx
                })
                
                logging.info(f"Saved: {filename}")
                
            except Exception as e:
                logging.error(f"Error processing box {box_idx+1}: {e}")
                continue
        
        return saved_images

    def determine_question_range(self, page_num, questions_per_page=5):
        """Determine expected question range for a given page."""
        start_qno = (page_num - 2) * questions_per_page + 1  # Assuming page 2 starts with Q1
        end_qno = start_qno + questions_per_page - 1
        return start_qno, end_qno

    def clean_json_response_unicode_safe(self, response_text):
        """Clean JSON response while preserving Unicode characters and fixing LaTeX escape issues."""
        try:
            # Remove code block markers
            response_text = response_text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            elif response_text.startswith('```'):
                response_text = response_text[3:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]

            response_text = response_text.strip()

            # Fix LaTeX escape sequences that break JSON parsing
            # Double escape backslashes in LaTeX commands
            latex_commands = ['frac', 'begin', 'end', 'left', 'right', 'sqrt', 'int', 'sum', 'lim',
                            'sin', 'cos', 'tan', 'log', 'ln', 'alpha', 'beta', 'gamma', 'delta',
                            'theta', 'pi', 'sigma', 'omega', 'infty', 'partial', 'nabla']

            for cmd in latex_commands:
                # Replace \command with \\command to fix JSON parsing
                response_text = response_text.replace(f'\\{cmd}', f'\\\\{cmd}')

            # Fix other problematic escape sequences
            response_text = re.sub(r'\\(?![a-zA-Z{$"\\\/bfnrt])', r'\\\\', response_text)

            # Fix common JSON formatting issues while preserving Unicode
            # Remove any trailing commas before closing brackets/braces
            response_text = re.sub(r',(\s*[}$$])', r'\1', response_text)

            # Ensure proper JSON array format
            if not response_text.startswith('['):
                response_text = '[' + response_text
            if not response_text.endswith(']'):
                response_text = response_text + ']'

            return response_text
        except Exception as e:
            logging.error(f"Error cleaning JSON response: {e}")
            return response_text

    def validate_and_clean_questions(self, questions):
        """Validate and clean extracted questions while preserving Unicode."""
        cleaned_questions = []
        
        for q in questions:
            if not isinstance(q, dict):
                continue
                
            # Ensure required fields exist
            cleaned_q = {
                'qno': str(q.get('qno', '')).strip(),
                'language': str(q.get('language', 'English')).strip(),
                'question': str(q.get('question', '')).strip(),
                'optiona': str(q.get('optiona', '')).strip(),
                'optionb': str(q.get('optionb', '')).strip(),
                'optionc': str(q.get('optionc', '')).strip(),
                'optiond': str(q.get('optiond', '')).strip(),
                'table_data': q.get('table_data', []),
                'type_of_question': str(q.get('type_of_question', 'MCQ')).strip(),
                'mark': str(q.get('mark', '1')).strip()
            }
            
            # Skip if no question text
            if not cleaned_q['question']:
                continue
                
            # Clean question number
            cleaned_q['qno'] = re.sub(r'^[Qq]', '', cleaned_q['qno'])
            
            # Validate language
            if cleaned_q['language'].lower() not in ['english', 'tamil']:
                cleaned_q['language'] = 'English'
            else:
                cleaned_q['language'] = cleaned_q['language'].title()
            
            cleaned_questions.append(cleaned_q)
        
        return cleaned_questions

    def validate_latex_syntax(self, question):
        """Validate and fix LaTeX syntax issues with proper delimiters."""
        fields_to_check = ['question', 'optiona', 'optionb', 'optionc', 'optiond']
        
        for field in fields_to_check:
            if field in question and question[field]:
                content = question[field]
                
                # Fix triple dollar signs to double (common error)
                content = re.sub(r'\$\$\$([^$]+)\$\$\$', r'$$\1$$', content)
                
                # Don't modify already properly formatted display math ($$...$$)
                # Only fix standalone single dollars that aren't part of display math
                # Use negative lookbehind and lookahead to avoid touching $$
                content = re.sub(r'(?<!\$)\$([^$\n]+)\$(?!\$)', r'$$\1$$', content)
                
                # Fix common LaTeX command formatting
                content = re.sub(r'\\frac\s*\{([^}]+)\}\s*\{([^}]+)\}', r'\\frac{\1}{\2}', content)
                content = re.sub(r'\\sqrt\s*\{([^}]+)\}', r'\\sqrt{\1}', content)
                content = re.sub(r'\\sum_\s*\{([^}]+)\}', r'\\sum_{\1}', content)
                content = re.sub(r'\\int_\s*\{([^}]+)\}', r'\\int_{\1}', content)
                content = re.sub(r'\\lim_\s*\{([^}]+)\}', r'\\lim_{\1}', content)
                
                # Fix subscripts and superscripts
                content = re.sub(r'([a-zA-Z])_([a-zA-Z0-9]+)(?![{}])', r'\1_{\2}', content)
                content = re.sub(r'([a-zA-Z])\^([a-zA-Z0-9]+)(?![{}])', r'\1^{\2}', content)
                
                # Replace Unicode mathematical symbols with LaTeX equivalents
                unicode_to_latex = {
                    'θ': '\\theta',
                    'π': '\\pi',
                    'α': '\\alpha',
                    'β': '\\beta',
                    'γ': '\\gamma',
                    'δ': '\\delta',
                    'ε': '\\epsilon',
                    'λ': '\\lambda',
                    'μ': '\\mu',
                    'σ': '\\sigma',
                    'φ': '\\phi',
                    'ψ': '\\psi',
                    'ω': '\\omega',
                    '∞': '\\infty',
                    '±': '\\pm',
                    '∓': '\\mp',
                    '≈': '\\approx',
                    '≠': '\\neq',
                    '≥': '\\geq',
                    '≤': '\\leq',
                    '»': '\\gg',
                    '«': '\\ll',
                    '∫': '\\int',
                    '∑': '\\sum',
                    '∏': '\\prod',
                    '√': '\\sqrt',
                    '∂': '\\partial',
                    '∇': '\\nabla',
                    '∆': '\\Delta',
                    '∈': '\\in',
                    '∉': '\\notin',
                    '⊂': '\\subset',
                    '⊃': '\\supset',
                    '∪': '\\cup',
                    '∩': '\\cap',
                    '→': '\\rightarrow',
                    '←': '\\leftarrow',
                    '↔': '\\leftrightarrow',
                    '⇒': '\\Rightarrow',
                    '⇐': '\\Leftarrow',
                    '⇔': '\\Leftrightarrow'
                }
                
                for unicode_char, latex_cmd in unicode_to_latex.items():
                    content = content.replace(unicode_char, latex_cmd)
                
                question[field] = content
        
        return question

    def process_table_data(self, table_data):
        """Process table data to ensure proper list of lists format [['a','b'],['c','d']]."""
        if not table_data:
            return []
        
        # If it's already a proper list of lists
        if isinstance(table_data, list):
            processed_table = []
            for row in table_data:
                if isinstance(row, list):
                    # Already a list - convert all elements to strings
                    processed_row = [str(cell) for cell in row]
                    processed_table.append(processed_row)
                elif isinstance(row, dict):
                    # Convert dict to list of values
                    processed_row = [str(value) for value in row.values()]
                    processed_table.append(processed_row)
                elif isinstance(row, set):
                    # Convert set to list
                    processed_row = [str(item) for item in row]
                    processed_table.append(processed_row)
                elif isinstance(row, (str, int, float)):
                    # Single value - make it a single-item list
                    processed_table.append([str(row)])
                else:
                    # Try to convert to string and make single-item list
                    processed_table.append([str(row)])
            return processed_table
        
        # If it's a string representation, try to parse it
        elif isinstance(table_data, str):
            if not table_data.strip():
                return []
            try:
                # Try to evaluate as Python literal (safe for lists)
                import ast
                parsed = ast.literal_eval(table_data)
                return self.process_table_data(parsed)  # Recursive call to handle parsed data
            except:
                # If parsing fails, return as single cell table
                return [[table_data]]
        
        # For any other type, convert to string and return as single cell
        else:
            return [[str(table_data)]]

    def clean_json_response_with_latex(self, response_text):
        """Clean JSON response while preserving LaTeX mathematical content."""
        try:
            # Remove code block markers
            response_text = response_text.strip()
            if response_text.startswith('```json'):
                response_text = response_text[7:]
            elif response_text.startswith('```'):
                response_text = response_text[3:]
            if response_text.endswith('```'):
                response_text = response_text[:-3]
            
            response_text = response_text.strip()
            
            # Preserve LaTeX content by temporarily replacing it
            latex_patterns = []
            latex_counter = 0
            
            # Find and preserve $$....$$ patterns (display math)
            def preserve_latex(match):
                nonlocal latex_counter
                placeholder = f"__LATEX_PLACEHOLDER_{latex_counter}__"
                latex_patterns.append((placeholder, match.group(0)))
                latex_counter += 1
                return placeholder
            
            # Preserve LaTeX expressions (display math only)
            response_text = re.sub(r'\$\$[^$]*?\$\$', preserve_latex, response_text, flags=re.DOTALL)
            
            # Fix common JSON formatting issues
            response_text = re.sub(r',(\s*[}\]])', r'\1', response_text)
            
            # Ensure proper JSON array format
            if not response_text.startswith('['):
                response_text = '[' + response_text
            if not response_text.endswith(']'):
                response_text = response_text + ']'
            
            # Restore LaTeX content
            for placeholder, original in latex_patterns:
                response_text = response_text.replace(placeholder, original)
            
            return response_text
        except Exception as e:
            logging.error(f"Error cleaning JSON response with LaTeX: {e}")
            return response_text

    def map_images_to_questions_unified(self, questions, saved_images):
        """Unified image mapping - replace <image> tags with exact image filenames for Excel storage."""

        def normalize_language(lang):
            return lang.strip().title()

        def normalize_qno(qno):
            return str(qno).strip().lstrip('Q')

        def get_possible_keys(qno, lang):
            q = normalize_qno(qno)
            l = normalize_language(lang)
            return [f"{q}_{l}", f"Q{q}_{l}", q, f"Q{q}"]

        # Build image lookup table with exact filenames
        image_lookup = {}

        logging.debug(f"DEBUG: Total saved images: {len(saved_images)}")
        for img_info in saved_images:
            try:
                question_num = normalize_qno(img_info['question'])
                language = normalize_language(img_info['language'])
                filename = img_info['filename']  # Use exact filename
                img_type = img_info['type'].lower()

                possible_keys = get_possible_keys(question_num, language)

                for key in possible_keys:
                    if key not in image_lookup:
                        image_lookup[key] = {
                            'main_diagrams': [],
                            'options': {'A': [], 'B': [], 'C': [], 'D': []}
                        }

                    if img_type == 'main_diagram' or 'diagram' in filename.lower():
                        if filename not in image_lookup[key]['main_diagrams']:
                            image_lookup[key]['main_diagrams'].append(filename)
                    elif img_type.startswith('option_') or 'option' in filename.lower():
                        match = re.search(r'option([A-D])', filename, re.IGNORECASE)
                        if match:
                            option_letter = match.group(1).upper()
                            if filename not in image_lookup[key]['options'][option_letter]:
                                image_lookup[key]['options'][option_letter].append(filename)
            except Exception as e:
                logging.warning(f"WARNING: Skipping image entry due to error: {e} -> {img_info}")

        # Debug: Show lookup map
        logging.debug(f"DEBUG: Constructed image_lookup with keys: {list(image_lookup.keys())}")
        for k, v in image_lookup.items():
            logging.info(f"  {k} -> Main: {len(v['main_diagrams'])}, Options: { {opt: len(imgs) for opt, imgs in v['options'].items() if imgs} }")

        # Map images to questions with exact filenames
        for question in questions:
            qno = normalize_qno(question.get('qno', ''))
            language = normalize_language(question.get('language', ''))

            possible_keys = get_possible_keys(qno, language)

            question.setdefault('image_placeholder', "")
            # Fixed: Don't convert table_data to string here, keep it as processed list
            if 'table_data' not in question:
                question['table_data'] = []
            question.setdefault('type_of_question', "MCQ")
            question.setdefault('mark', "1")

            img_data = None
            matched_key = None
            for key in possible_keys:
                if key in image_lookup:
                    img_data = image_lookup[key]
                    matched_key = key
                    break

            logging.debug(f"DEBUG: Processing Q{qno}_{language} → Matched key: {matched_key}")

            # Map main diagram with relative path
            if img_data:
                question_text = question.get('question', '')
                if img_data['main_diagrams']:
                    main_image = img_data['main_diagrams'][0]  # Exact filename
                    # Add relative path for Excel compatibility
                    main_image_path = f"images/{main_image}"
                    if '<image>' in question_text:
                        question['question'] = question_text.replace('<image>', '', 1)
                    question['image_placeholder'] = main_image_path  # Store relative path

                # Map options with exact filenames
                for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
                    if opt_key in question:
                        opt_text = str(question[opt_key]).strip()
                        available_images = img_data['options'].get(opt_letter, [])

                        logging.debug(f"DEBUG: Q{qno}_{language} Option {opt_letter} → Images: {available_images}")

                        if available_images:
                            exact_filename = available_images[0]  # Use exact filename
                            # Add relative path for Excel compatibility
                            image_path = f"images/{exact_filename}"
                            if opt_text.lower() == '<image>':
                                question[opt_key] = image_path  # Store relative path
                            elif '<image>' in opt_text:
                                question[opt_key] = opt_text.replace('<image>', image_path)
                        else:
                            if opt_text.lower() == '<image>':
                                question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                            elif '<image>' in opt_text:
                                question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')
            else:
                # No match found for this question
                logging.debug(f"DEBUG: No images found for Q{qno}_{language}")
                if '<image>' in question.get('question', ''):
                    question['image_placeholder'] = "IMAGE_MISSING"
                for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
                    if opt_key in question:
                        opt_text = str(question[opt_key]).strip()
                        if opt_text.lower() == '<image>':
                            question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                        elif '<image>' in opt_text:
                            question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')

        return questions

    def is_logo_region(self, box_coords, page_width, page_height):
        """Enhanced logo detection to filter out logos at the end of PDF pages."""
        x1, y1, x2, y2 = box_coords

        # Calculate relative positions
        rel_x1, rel_y1 = x1 / page_width, y1 / page_height
        rel_x2, rel_y2 = x2 / page_width, y2 / page_height

        # Logo typically appears in bottom area of pages
        bottom_threshold = 0.85  # Bottom 15% of page

        # Check if box is in bottom area (likely logo)
        if rel_y1 > bottom_threshold:
            logging.info(f"Filtering out logo region: ({x1},{y1},{x2},{y2}) - bottom area")
            return True

        # Check for very small boxes (likely artifacts)
        box_width, box_height = x2 - x1, y2 - y1
        if box_width < 50 or box_height < 50:
            logging.info(f"Filtering out small box: ({x1},{y1},{x2},{y2}) - too small")
            return True

        # Check for very wide boxes spanning most of page width (likely headers/footers)
        if (x2 - x1) > 0.8 * page_width and (y2 - y1) < 0.1 * page_height:
            logging.info(f"Filtering out header/footer: ({x1},{y1},{x2},{y2}) - wide and thin")
            return True

        return False

    def detect_bounding_boxes(self, image_path):
        """Enhanced YOLO detection with logo filtering for better accuracy."""
        try:
            model = YOLO(r'src/yolo/best.pt')

            results = model.predict(
                source=image_path,
                imgsz=640,
                conf=0.05,  # Slightly higher confidence to reduce noise
                iou=0.3,    # Higher IoU to reduce overlapping detections
                task='detect',
                verbose=False
            )

            if not results[0].boxes:
                return []

            # Get image dimensions for logo filtering
            img = cv2.imread(image_path)
            page_height, page_width = img.shape[:2]

            # Filter boxes and remove logos
            filtered_boxes = []
            for box in results[0].boxes:
                x1, y1, x2, y2 = map(int, box.xyxy[0].tolist())

                # Skip logo regions
                if self.is_logo_region((x1, y1, x2, y2), page_width, page_height):
                    continue

                filtered_boxes.append((x1, y1, x2, y2))

            logging.info(f"YOLO detected {len(results[0].boxes)} boxes, kept {len(filtered_boxes)} after logo filtering")
            return filtered_boxes

        except Exception as e:
            logging.error(f"YOLO detection error: {e}")
            return []

    def extract_questions_with_sequential_processing(self, question_pdf, start_page, end_page, max_questions=30):
        """
        Enhanced extraction with sequential question-by-question processing.
        Each question is completely processed (text + image mapping) before moving to next.
        """
        all_questions = []
        pdf_name = os.path.basename(question_pdf)
        question_counter = 0
        processed_questions = set()  # Track processed question numbers to avoid duplicates

        for page_num in range(start_page, end_page + 1):
            if question_counter >= max_questions:
                logging.info(f"Reached maximum questions limit ({max_questions}), stopping")
                print(f"🛑 Reached maximum questions limit ({max_questions}), stopping")
                break

            print(f"\n📄 Processing page {page_num} of {end_page}")
            logging.info(f"\n=== Processing page {page_num} ===")

            # Convert PDF page to image for YOLO detection
            try:
                doc = fitz.open(question_pdf)
                page = doc[page_num - 1]
                temp_img = tempfile.NamedTemporaryFile(suffix=".jpg", delete=False)
                temp_img.close()
                page.get_pixmap(dpi=300).save(temp_img.name)
                doc.close()

                # Detect and classify images with enhanced logo filtering
                print(f"🔍 Detecting images on page {page_num}...")
                boxes = self.detect_bounding_boxes(temp_img.name)
                print(f"📦 Found {len(boxes)} bounding boxes after logo filtering")
                logging.info(f"Detected {len(boxes)} bounding boxes after logo filtering")

                saved_images = []
                if boxes:
                    print(f"🤖 Classifying {len(boxes)} detected regions...")
                    classification_response = self.identify_and_classify_images(temp_img.name, boxes)
                    logging.info(f"Classification response: {classification_response}")

                    classified_images = self.parse_image_classification(classification_response)
                    print(f"✅ Found {len(classified_images)} valid visual elements")
                    logging.info(f"Found {len(classified_images)} valid visual elements")

                    # Process and save ONLY true visual images
                    if classified_images:
                        print(f"💾 Saving {len(classified_images)} images...")
                    saved_images = self.process_and_save_classified_images(
                        temp_img.name, boxes, classified_images, page_num, pdf_name
                    )
                    print(f"🖼️  Successfully saved {len(saved_images)} images")
                else:
                    print(f"❌ No images detected on page {page_num}")

                os.remove(temp_img.name)

            except Exception as e:
                logging.error(f"Error processing images on page {page_num}: {e}")
                saved_images = []

            # Extract questions with sequential processing approach
            print(f"📝 Extracting questions from page {page_num}...")
            try:
                with open(question_pdf, "rb") as q_file:
                    question_data = q_file.read()

                # Enhanced prompt for sequential question extraction
                enhanced_prompt = f"""Extract ALL questions from page {page_num} of this Physics question paper:

    CRITICAL REQUIREMENTS:
    1. Extract ALL questions that appear on this page, including:
       - Questions that start on this page
       - Questions that continue from previous pages (extract the part on this page)
       - Questions that start on this page but continue to next page
    2. Use the EXACT question number shown in the PDF (1, 2, 3, etc.)
    3. Use <image> placeholder ONLY for actual diagrams, figures, or visual content (NOT for mathematical formulas)
    4. Mathematical expressions should be written in LaTeX format using $$...$$ delimiters
    5. For multipage questions: If a question starts on this page but options appear on next page, include empty strings for missing options
    6. For questions with incomplete options: Extract all visible options and use empty strings for missing ones
    7. DO NOT include blooms_level or type_of_question fields (user doesn't want these)
    8. If a question spans multiple pages, extract whatever content is visible on this page

    QUESTION STRUCTURE:
    {{
        "qno": "1",
        "language": "English",
        "question": "Question text with $$mathematical expressions$$ and <image> for diagrams",
        "optiona": "Option A text with $$math$$ if needed",
        "optionb": "Option B text with $$math$$ if needed",
        "optionc": "Option C text with $$math$$ if needed",
        "optiond": "Option D text with $$math$$ if needed"
    }}

    IMPORTANT GUIDELINES:
    - Questions are numbered sequentially (1, 2, 3, etc.)
    - This is a Physics paper, so expect mathematical formulas, equations, and diagrams
    - Use <image> placeholder only for actual visual content (diagrams, figures, graphs)
    - Mathematical content should be in LaTeX format: $$E = mc^2$$, $$F = ma$$, etc.
    - If a question is incomplete on this page, extract what's available and note it
    - Focus on accuracy - better to extract fewer questions correctly than many incorrectly

    JSON OUTPUT FORMAT:
    [
    {{
        "qno": "1",
        "language": "English",
        "question": "A wheel of a bullock cart is rolling on a level road as shown in <image>. If its linear speed is $$v$$ in the direction shown, which one of the following options is correct?",
        "optiona": "Point P moves faster than point Q",
        "optionb": "Both the points P and Q move with equal speed",
        "optionc": "Point P has zero speed",
        "optiond": "Point P moves slower than point Q"
    }},
    {{
        "qno": "2",
        "language": "English",
        "question": "A thermodynamic system is taken through the cycle abcd. The work done by the gas along the path bc is:",
        "optiona": "$$30 J$$",
        "optionb": "$$-90 J$$",
        "optionc": "$$-60 J$$",
        "optiond": "$$0$$"
    }}
    ]

    CRITICAL NOTES:
    - Extract questions in the exact order they appear (1, 2, 3, etc.)
    - Use simple string format for question and options (not nested JSON objects)
    - Only use <image> for actual visual diagrams/figures
    - Use $$...$$ for mathematical expressions
    - DO NOT include blooms_level or type_of_question fields
    """

                # Configure Gemini for precise question extraction
                generation_config = {
                    "temperature": 0.1,  # Low temperature for consistent extraction
                    "top_p": 0.8,
                    "top_k": 40,
                    "max_output_tokens": 8192,
                }

                response = self.model.generate_content(
                    [
                        {"mime_type": "application/pdf", "data": question_data},
                        enhanced_prompt
                    ],
                    generation_config=generation_config
                )

                if response and hasattr(response, "text") and response.text.strip():
                    try:
                        print(f"🧠 Processing Gemini response for page {page_num}...")
                        # Clean and parse JSON response
                        response_text = self.clean_json_response_unicode_safe(response.text)
                        questions = json.loads(response_text, strict=False)

                        print(f"📋 Found {len(questions)} questions on page {page_num}")

                        # Validate and clean extracted questions
                        questions = self.validate_and_clean_questions_simple_format(questions)
                        print(f"✅ Validated {len(questions)} questions")

                        # Sequential processing: map images to each question immediately
                        page_question_count = 0
                        for question in questions:
                            if question_counter >= max_questions:
                                break

                            qno = question.get('qno', 'unknown')
                            clean_qno = re.sub(r'^[Qq]', '', str(qno)).strip()

                            # Check if this question has already been processed
                            if clean_qno in processed_questions:
                                print(f"⏭️  Skipping Question {qno} - already processed")
                                logging.info(f"Skipping duplicate question {qno}")
                                continue

                            print(f"\n🔄 Processing Question {qno} (#{question_counter + 1} overall)")

                            # Map images to this specific question
                            question = self.map_images_to_single_question(question, saved_images, page_num)
                            all_questions.append(question)
                            processed_questions.add(clean_qno)  # Mark as processed
                            question_counter += 1
                            page_question_count += 1

                            print(f"✅ Completed Question {qno} - Page total: {page_question_count}, Overall total: {question_counter}")
                            logging.info(f"Processed question {qno} - Total: {question_counter}")

                        print(f"📄 Page {page_num} complete: {page_question_count} questions processed")

                    except json.JSONDecodeError as e:
                        print(f"❌ JSON parsing error on page {page_num}: {e}")
                        logging.error(f"JSON parsing error on page {page_num}: {e}")
                        logging.error(f"Problematic response: {response.text[:500]}...")
                else:
                    print(f"❌ No valid response from Gemini for page {page_num}")

            except Exception as e:
                logging.error(f"Error extracting questions from page {page_num}: {e}")

            # Brief pause between pages
            time.sleep(1)

        logging.info(f"Sequential processing complete. Extracted {len(all_questions)} questions total.")

        # Post-process to merge incomplete multipage questions
        all_questions = self.merge_multipage_questions(all_questions)
        logging.info(f"After merging multipage questions: {len(all_questions)} questions total.")

        return all_questions

    def merge_multipage_questions(self, questions):
        """Merge questions that span multiple pages by combining incomplete options."""
        merged_questions = []
        question_dict = {}

        # Group questions by question number
        for q in questions:
            qno = re.sub(r'^[Qq]', '', str(q.get('qno', ''))).strip()
            if qno not in question_dict:
                question_dict[qno] = []
            question_dict[qno].append(q)

        # Merge questions with the same number
        for qno, question_list in question_dict.items():
            if len(question_list) == 1:
                # Single question, no merging needed
                merged_questions.append(question_list[0])
            else:
                # Multiple instances of same question - merge them
                print(f"🔗 Merging {len(question_list)} instances of question {qno}")
                merged_q = self.merge_question_instances(question_list)
                merged_questions.append(merged_q)

        return merged_questions

    def merge_question_instances(self, question_list):
        """Merge multiple instances of the same question into one complete question."""
        # Start with the first instance
        merged = question_list[0].copy()

        # Merge content from other instances
        for q in question_list[1:]:
            # Merge question text (take the longest one)
            if len(str(q.get('question', ''))) > len(str(merged.get('question', ''))):
                merged['question'] = q.get('question', merged.get('question', ''))

            # Merge options (fill in missing ones)
            for opt_key in ['optiona', 'optionb', 'optionc', 'optiond']:
                if not merged.get(opt_key, '').strip() and q.get(opt_key, '').strip():
                    merged[opt_key] = q.get(opt_key, '')
                elif len(str(q.get(opt_key, ''))) > len(str(merged.get(opt_key, ''))):
                    merged[opt_key] = q.get(opt_key, merged.get(opt_key, ''))

            # Merge image placeholder (take non-empty one)
            if not merged.get('image_placeholder', '').strip() and q.get('image_placeholder', '').strip():
                merged['image_placeholder'] = q.get('image_placeholder', '')

        return merged

    def validate_and_clean_questions_simple_format(self, questions):
        """Validate and clean extracted questions in simple format (no nested structures)."""
        cleaned_questions = []

        for q in questions:
            if not isinstance(q, dict):
                continue

            # Simple format validation
            cleaned_q = {
                'qno': str(q.get('qno', '')).strip(),
                'language': str(q.get('language', 'English')).strip(),
                'question': str(q.get('question', '')).strip(),
                'optiona': str(q.get('optiona', '')).strip(),
                'optionb': str(q.get('optionb', '')).strip(),
                'optionc': str(q.get('optionc', '')).strip(),
                'optiond': str(q.get('optiond', '')).strip(),
            }

            # Skip if no question text
            if not cleaned_q['question']:
                continue

            # Clean question number
            cleaned_q['qno'] = re.sub(r'^[Qq]', '', cleaned_q['qno'])

            # Validate language
            if cleaned_q['language'].lower() not in ['english', 'tamil']:
                cleaned_q['language'] = 'English'
            else:
                cleaned_q['language'] = cleaned_q['language'].title()

            # Fix LaTeX syntax
            for field in ['question', 'optiona', 'optionb', 'optionc', 'optiond']:
                cleaned_q[field] = self.fix_latex_syntax(cleaned_q[field])

            cleaned_questions.append(cleaned_q)

        return cleaned_questions

    def map_images_to_single_question(self, question, saved_images, page_num):
        """Map images to a single question using improved logic with better matching."""
        qno = str(question.get('qno', '')).strip()

        # Clean question number for better matching
        clean_qno = re.sub(r'^[Qq]', '', qno).strip()

        # Find images that belong to this question with multiple matching strategies
        question_images = []

        # Strategy 1: Exact question number match
        for img_info in saved_images:
            img_qno = str(img_info.get('question', '')).strip()
            clean_img_qno = re.sub(r'^[Qq]', '', img_qno).strip()

            if clean_img_qno == clean_qno:
                question_images.append(img_info['filename'])
                print(f"🎯 Mapped image {img_info['filename']} to question {clean_qno} (exact match)")

        # Strategy 2: If no exact match, try fuzzy matching for nearby questions (but be more careful)
        if not question_images:
            try:
                target_num = int(clean_qno)
                best_match = None
                best_distance = float('inf')

                for img_info in saved_images:
                    img_qno = str(img_info.get('question', '')).strip()
                    clean_img_qno = re.sub(r'^[Qq]', '', img_qno).strip()
                    try:
                        img_num = int(clean_img_qno)
                        distance = abs(img_num - target_num)

                        # Only allow fuzzy matching within ±1 and prefer closer matches
                        if distance <= 1 and distance < best_distance:
                            best_match = img_info
                            best_distance = distance
                    except ValueError:
                        continue

                if best_match and best_distance <= 1:
                    question_images.append(best_match['filename'])
                    print(f"🔄 Mapped image {best_match['filename']} to question {clean_qno} (fuzzy match from Q{best_match['question']}, distance: {best_distance})")

            except ValueError:
                pass

        # Handle image mapping with proper relative paths
        question_text = question.get('question', '')

        # Separate images by type (main diagrams vs options)
        main_images = []
        option_images = {'A': [], 'B': [], 'C': [], 'D': []}

        for img_info in saved_images:
            img_qno = str(img_info.get('question', '')).strip()
            clean_img_qno = re.sub(r'^[Qq]', '', img_qno).strip()

            if clean_img_qno == clean_qno:
                img_type = img_info.get('type', '')
                filename = img_info['filename']

                if img_type == 'main_diagram' or 'diagram' in filename.lower():
                    main_images.append(filename)
                elif img_type.startswith('option_') or 'option' in filename.lower():
                    # Extract option letter from filename or type
                    match = re.search(r'option([A-D])', filename, re.IGNORECASE)
                    if match:
                        option_letter = match.group(1).upper()
                        option_images[option_letter].append(filename)

        # Handle main question image
        if main_images:
            # Create relative path for the main image
            image_relative_path = f"images/{main_images[0]}"

            if '<image>' in question_text:
                # Replace <image> placeholder
                question['question'] = question_text.replace('<image>', '', 1).strip()
                print(f"✅ Question {clean_qno}: Removed <image> placeholder, image will be in image_placeholder field")

            # Store the relative path in image_placeholder field for Excel output
            question['image_placeholder'] = image_relative_path
            print(f"✅ Question {clean_qno}: Set image_placeholder to {image_relative_path}")
        else:
            # No main images found
            if '<image>' in question_text:
                question['question'] = question_text.replace('<image>', '').strip()
                question['image_placeholder'] = "IMAGE_MISSING"
                print(f"⚠️  Question {clean_qno}: Removed <image> placeholder, marked as IMAGE_MISSING")
            else:
                question['image_placeholder'] = ""

        # Handle option images
        for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
            if opt_key in question:
                opt_text = str(question[opt_key]).strip()
                available_images = option_images.get(opt_letter, [])

                if available_images:
                    # Create relative path for option image
                    option_image_path = f"images/{available_images[0]}"
                    if opt_text.lower() == '<image>':
                        question[opt_key] = option_image_path
                        print(f"✅ Question {clean_qno} Option {opt_letter}: Replaced <image> with {option_image_path}")
                    elif '<image>' in opt_text:
                        question[opt_key] = opt_text.replace('<image>', option_image_path)
                        print(f"✅ Question {clean_qno} Option {opt_letter}: Replaced <image> in text with {option_image_path}")
                else:
                    # No option image found
                    if opt_text.lower() == '<image>':
                        question[opt_key] = f'IMAGE_MISSING_OPTION_{opt_letter}'
                        print(f"⚠️  Question {clean_qno} Option {opt_letter}: No image found, marked as missing")
                    elif '<image>' in opt_text:
                        question[opt_key] = opt_text.replace('<image>', f'IMAGE_MISSING_OPTION_{opt_letter}')
                        print(f"⚠️  Question {clean_qno} Option {opt_letter}: No image found, marked as missing in text")

        if not main_images and not any(option_images.values()):
            print(f"⚠️  Question {clean_qno}: No images found")

        return question

    def clean_text_for_excel(self, text):
        """Clean text to make it compatible with Excel export."""
        if not text:
            return ""

        # Convert to string if not already
        text = str(text)

        # Remove or replace problematic characters for Excel
        # Remove control characters (0x00-0x1F except tab, newline, carriage return)
        import re
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        # Replace problematic LaTeX characters that cause Excel issues
        text = text.replace('\\frac', '\\\\frac')
        text = text.replace('\\begin', '\\\\begin')
        text = text.replace('\\end', '\\\\end')
        text = text.replace('\\left', '\\\\left')
        text = text.replace('\\right', '\\\\right')

        # Limit length to prevent Excel issues
        if len(text) > 32767:  # Excel cell limit
            text = text[:32760] + "..."

        return text

    def validate_and_clean_questions_with_structured_format(self, questions):
        """Validate and clean extracted questions with new structured format."""
        cleaned_questions = []
        
        for q in questions:
            if not isinstance(q, dict):
                continue
            
            # Initialize structured question format
            cleaned_q = {
                'qno': str(q.get('qno', '')).strip(),
                'language': str(q.get('language', 'English')).strip(),
                'question': self.structure_question_field(q.get('question', {})),
                'optiona': self.structure_option_field(q.get('optiona', {})),
                'optionb': self.structure_option_field(q.get('optionb', {})),
                'optionc': self.structure_option_field(q.get('optionc', {})),
                'optiond': self.structure_option_field(q.get('optiond', {})),
                'type_of_question': str(q.get('type_of_question', 'MCQ')).strip(),
                'mark': str(q.get('mark', '1')).strip(),
                'blooms_level': self.validate_blooms_level(q.get('blooms_level', ''))
            }
            
            # Skip if no question text
            if not cleaned_q['question'].get('text', '').strip():
                continue
            
            # Clean question number
            cleaned_q['qno'] = re.sub(r'^[Qq]', '', cleaned_q['qno'])
            
            # Validate language
            if cleaned_q['language'].lower() not in ['english', 'tamil']:
                cleaned_q['language'] = 'English'
            else:
                cleaned_q['language'] = cleaned_q['language'].title()
            
            # Validate LaTeX syntax in structured format
            cleaned_q = self.validate_latex_syntax_structured(cleaned_q)
            
            cleaned_questions.append(cleaned_q)
        
        return cleaned_questions

    def structure_question_field(self, question_data):
        """Structure question field without separate latex_content."""
        if isinstance(question_data, str):
            # If it's a string, parse it as legacy format
            return {
                "text": question_data,
                "image": [],
                "tables": {}
            }
        elif isinstance(question_data, dict):
            # Integrate latex_content into text if it exists separately
            text = str(question_data.get('text', ''))
            latex_content = str(question_data.get('latex_content', ''))
            
            # If latex_content exists separately, integrate it into text
            if latex_content and latex_content not in text:
                if text:
                    text = f"{text} {latex_content}"
                else:
                    text = latex_content
            
            return {
                "text": text,
                "image": question_data.get('image', []) if isinstance(question_data.get('image'), list) else [question_data.get('image', '')],
                "tables": question_data.get('tables', {}) if isinstance(question_data.get('tables'), dict) else {}
            }
        else:
            return {
                "text": str(question_data),
                "image": [],
                "tables": {}
            }


    def structure_option_field(self, option_data):
        """Structure option field without separate latex attribute."""
        if isinstance(option_data, str):
            # If it's a string, parse it as legacy format
            return {
                "text": option_data,
                "img": ""
            }
        elif isinstance(option_data, dict):
            # Integrate latex into text if it exists separately
            text = str(option_data.get('text', ''))
            latex = str(option_data.get('latex', ''))
            
            # If latex exists separately, integrate it into text
            if latex and latex not in text:
                if text:
                    text = f"{text} {latex}"
                else:
                    text = latex
            
            return {
                "text": text,
                "img": str(option_data.get('img', ''))
            }
        else:
            return {
                "text": str(option_data),
                "img": ""
            }


    def validate_blooms_level(self, blooms_level):
        """Validate and ensure blooms_level is one of the required values."""
        valid_levels = ["Applying", "Remembering", "Understanding", "Analyzing"]
        
        if blooms_level and blooms_level in valid_levels:
            return blooms_level
        
        # If empty or invalid, predict based on common patterns
        # This is a simple prediction - in real implementation, you might want more sophisticated logic
        return "Understanding"  # Default to Understanding if not specified

    def validate_latex_syntax_structured(self, question):
        """Validate and fix LaTeX syntax in integrated text fields."""
        # Validate LaTeX in question text field
        if 'text' in question['question']:
            question['question']['text'] = self.fix_latex_syntax(question['question']['text'])
        
        # Validate LaTeX in option text fields
        for opt_key in ['optiona', 'optionb', 'optionc', 'optiond']:
            if 'text' in question[opt_key]:
                question[opt_key]['text'] = self.fix_latex_syntax(question[opt_key]['text'])
        
        return question

    def fix_latex_syntax(self, content):
        """Fix common LaTeX syntax issues."""
        if not content:
            return content
        
        # Fix triple dollar signs to double (common error)
        content = re.sub(r'\$\$\$([^$]+)\$\$\$', r'$$\1$$', content)
        
        # Don't modify already properly formatted display math ($$...$$)
        # Only fix standalone single dollars that aren't part of display math
        content = re.sub(r'(?<!\$)\$([^$\n]+)\$(?!\$)', r'$$\1$$', content)
        
        # Fix common LaTeX command formatting
        content = re.sub(r'\\frac\s*\{([^}]+)\}\s*\{([^}]+)\}', r'\\frac{\1}{\2}', content)
        content = re.sub(r'\\sqrt\s*\{([^}]+)\}', r'\\sqrt{\1}', content)
        
        # Fix subscripts and superscripts
        content = re.sub(r'([a-zA-Z])_([a-zA-Z0-9]+)(?![{}])', r'\1_{\2}', content)
        content = re.sub(r'([a-zA-Z])\^([a-zA-Z0-9]+)(?![{}])', r'\1^{\2}', content)
        
        return content

    def map_images_to_questions_structured_format(self, questions, saved_images):
        """Map images to questions in structured format."""
        
        def normalize_language(lang):
            return lang.strip().title()

        def normalize_qno(qno):
            return str(qno).strip().lstrip('Q')

        def get_possible_keys(qno, lang):
            q = normalize_qno(qno)
            l = normalize_language(lang)
            return [f"{q}_{l}", f"Q{q}_{l}", q, f"Q{q}"]

        # Build image lookup table with exact filenames
        image_lookup = {}

        logging.debug(f"DEBUG: Total saved images: {len(saved_images)}")
        for img_info in saved_images:
            try:
                question_num = normalize_qno(img_info['question'])
                language = normalize_language(img_info['language'])
                filename = img_info['filename']
                img_type = img_info['type'].lower()

                possible_keys = get_possible_keys(question_num, language)

                for key in possible_keys:
                    if key not in image_lookup:
                        image_lookup[key] = {
                            'main_diagrams': [],
                            'options': {'A': [], 'B': [], 'C': [], 'D': []}
                        }

                    if img_type == 'main_diagram' or 'diagram' in filename.lower():
                        if filename not in image_lookup[key]['main_diagrams']:
                            image_lookup[key]['main_diagrams'].append(filename)
                    elif img_type.startswith('option_') or 'option' in filename.lower():
                        match = re.search(r'option([A-D])', filename, re.IGNORECASE)
                        if match:
                            option_letter = match.group(1).upper()
                            if filename not in image_lookup[key]['options'][option_letter]:
                                image_lookup[key]['options'][option_letter].append(filename)
            except Exception as e:
                logging.warning(f"WARNING: Skipping image entry due to error: {e} -> {img_info}")

        # Map images to questions in structured format
        for question in questions:
            qno = normalize_qno(question.get('qno', ''))
            language = normalize_language(question.get('language', ''))

            possible_keys = get_possible_keys(qno, language)

            img_data = None
            matched_key = None
            for key in possible_keys:
                if key in image_lookup:
                    img_data = image_lookup[key]
                    matched_key = key
                    break

            logging.debug(f"DEBUG: Processing Q{qno}_{language} → Matched key: {matched_key}")

            # Map main diagram images to question field
            if img_data and img_data['main_diagrams']:
                # Replace <image> placeholders with actual filenames
                question_images = []
                for img in question['question']['image']:
                    if img == '<image>':
                        question_images.extend(img_data['main_diagrams'])
                    else:
                        question_images.append(img)
                question['question']['image'] = question_images
            else:
                # Remove <image> placeholders if no images found
                question['question']['image'] = [img for img in question['question']['image'] if img != '<image>']

            # Map option images
            option_mapping = {'optiona': 'A', 'optionb': 'B', 'optionc': 'C', 'optiond': 'D'}
            
            for opt_key, opt_letter in option_mapping.items():
                if img_data and img_data['options'][opt_letter]:
                    # If option has placeholder, replace with actual filename
                    if question[opt_key]['img'] == '<image>' or not question[opt_key]['img']:
                        question[opt_key]['img'] = img_data['options'][opt_letter][0]
                else:
                    # Clear placeholder if no image found
                    if question[opt_key]['img'] == '<image>':
                        question[opt_key]['img'] = ""

        return questions

    def save_to_excel_with_simple_format(self, questions, excel_path):
        """Save questions to Excel with only the 4 required columns as specified by user."""
        try:
            # Define Excel columns - only the 4 required columns (excluding blooms_level and type_of_question)
            excel_columns = [
                'question', 'optiona', 'optionb', 'optionc', 'optiond'
            ]
            
            # Prepare questions for simple Excel format (only 4 columns)
            for question in questions:
                # Ensure all required fields exist as simple strings
                for col in excel_columns:
                    if col not in question:
                        question[col] = ""

                # Handle image references in question text
                question_text = str(question.get('question', ''))
                image_placeholder = question.get('image_placeholder', '')

                # Only add image reference if there's an image and no existing image reference
                if image_placeholder and image_placeholder != "IMAGE_MISSING" and "[Image:" not in question_text:
                    # Add image reference to question text with relative path
                    question['question'] = f"{question_text} [Image: {image_placeholder}]"
                elif '<image>' in question_text:
                    # Clean up any remaining <image> placeholders
                    if image_placeholder and image_placeholder != "IMAGE_MISSING":
                        question['question'] = question_text.replace('<image>', f"[Image: {image_placeholder}]")
                    else:
                        question['question'] = question_text.replace('<image>', '[IMAGE_MISSING]')

                # Ensure all fields are simple strings (not nested objects) and clean for Excel
                for col in excel_columns:
                    if isinstance(question[col], dict):
                        # If it's a dict, extract the text content
                        question[col] = str(question[col].get('text', ''))
                    else:
                        question[col] = str(question[col])

                    # Clean text for Excel compatibility
                    question[col] = self.clean_text_for_excel(question[col])
            
            # Create DataFrame with correct column order
            df = pd.DataFrame(questions)
            df = df[excel_columns]
            
            # Create workbook manually for better Unicode control
            wb = Workbook()
            ws = wb.active
            ws.title = "Questions_Simple_Format"
            
            # Add headers
            for col_idx, header in enumerate(excel_columns, 1):
                cell = ws.cell(row=1, column=col_idx, value=header)
                cell.font = Font(bold=True)
            
            # Add data rows with Unicode preservation
            for row_idx, question in enumerate(questions, 2):
                for col_idx, col_name in enumerate(excel_columns, 1):
                    cell_value = question.get(col_name, "")
                    
                    # Preserve Unicode and JSON characters exactly
                    if isinstance(cell_value, str):
                        ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    else:
                        ws.cell(row=row_idx, column=col_idx, value=str(cell_value))
            
            # Set column widths for better readability (only 5 columns now)
            column_widths = {
                'A': 120, # question (wide for question text with LaTeX and image references)
                'B': 80,  # optiona
                'C': 80,  # optionb
                'D': 80,  # optionc
                'E': 80,  # optiond
            }
            
            for col, width in column_widths.items():
                ws.column_dimensions[col].width = width
            
            # Set text wrapping for JSON columns
            for row in ws.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical='top')
            
            # Save with UTF-8 encoding
            wb.save(excel_path)
            logging.info(f"Excel file saved with simple format (4 columns): {excel_path}")

            return True

        except Exception as e:
            logging.error(f"Error saving to Excel with simple format: {e}")
            import traceback
            traceback.print_exc()
            return False

    def process_pdf(self, pdf_path, start_page=2, end_page=10, max_questions=30):
        """Main processing function with sequential processing and simple format support."""
        try:
            logging.info(f"Starting PDF processing: {pdf_path}")
            logging.info(f"Processing pages {start_page} to {end_page}, max questions: {max_questions}")

            # Clear tracking variables
            self.processed_image_signatures.clear()
            self.question_image_counter.clear()
            self.global_image_signatures.clear()

            # Extract questions with sequential processing
            questions = self.extract_questions_with_sequential_processing(pdf_path, start_page, end_page, max_questions)

            if not questions:
                logging.info("No questions extracted from the PDF")
                return False
            
            # Generate Excel path with PDF name
            pdf_basename = os.path.splitext(os.path.basename(pdf_path))[0]
            clean_pdf_name = re.sub(r'[^a-zA-Z0-9_]', '_', pdf_basename)
            excel_path = os.path.join(self.output_dir, f"{clean_pdf_name}_questions_simple.xlsx")

            # Save to Excel with simple format (4 columns only)
            success = self.save_to_excel_with_simple_format(questions, excel_path)
            
            if success:
                # Print simple summary
                total_questions = len(questions)
                questions_with_images = len([q for q in questions if q.get('image_placeholder')])
                questions_with_latex = len([q for q in questions if '$$' in str(q.get('question', ''))])

                logging.info(f"\n=== PROCESSING COMPLETE ===")
                logging.info(f"Excel file saved: {excel_path}")
                logging.info(f"Total questions extracted: {total_questions}")
                logging.info(f"Questions with images: {questions_with_images}")
                logging.info(f"Questions with LaTeX content: {questions_with_latex}")
                logging.info(f"Total unique images saved: {len(self.global_image_signatures)}")

                print(f"✅ Successfully processed {total_questions} questions")
                print(f"📊 Excel file saved: {excel_path}")
                print(f"🖼️  Questions with images: {questions_with_images}")
                print(f"🔢 Questions with LaTeX: {questions_with_latex}")
            
            return success
            
        except Exception as e:
            logging.error(f"Error processing PDF: {e}")
            import traceback
            traceback.print_exc()
            return False
