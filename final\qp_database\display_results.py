import pandas as pd
import os

def display_questions_with_images():
    """Display questions that have images correctly mapped from the Excel file."""
    
    # Path to the Excel file
    excel_path = "data/output/excel/clgdunia_questions_simple.xlsx"
    
    if not os.path.exists(excel_path):
        print("❌ Excel file not found. Please run the main processing first.")
        return
    
    # Load the Excel file
    try:
        df = pd.read_excel(excel_path)
        print(f"📊 Loaded {len(df)} questions from Excel file")
    except Exception as e:
        print(f"❌ Error loading Excel file: {e}")
        return
    
    print("\n" + "="*80)
    print("📋 QUESTIONS WITH CORRECTLY MAPPED IMAGES")
    print("="*80)
    
    # Find questions with images (those containing [Image: images/...])
    questions_with_images = df[df['question'].str.contains(r'\[Image: images/', na=False)]
    
    if len(questions_with_images) == 0:
        print("❌ No questions found with correctly mapped images.")
        return
    
    print(f"✅ Found {len(questions_with_images)} questions with images:\n")
    
    for index, row in questions_with_images.iterrows():
        qno = index + 1  # Question number (row index + 1)
        question_text = str(row['question'])
        
        # Extract image path from question text
        import re
        image_match = re.search(r'\[Image: (images/[^\]]+)\]', question_text)
        image_path = image_match.group(1) if image_match else "No image path found"
        
        # Clean question text (remove image reference for display)
        clean_question = re.sub(r'\s*\[Image: [^\]]+\]', '', question_text).strip()
        
        print(f"🔢 Question {qno}:")
        print(f"📝 Text: {clean_question[:150]}{'...' if len(clean_question) > 150 else ''}")
        print(f"🖼️  Image: {image_path}")
        
        # Check for option images
        option_images = []
        for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
            if opt_key in row and pd.notna(row[opt_key]):
                opt_text = str(row[opt_key])
                if 'images/' in opt_text:
                    option_images.append(f"Option {opt_letter}: {opt_text}")
        
        if option_images:
            print("📋 Option Images:")
            for opt_img in option_images:
                print(f"   {opt_img}")
        
        print("-" * 60)
    
    print(f"\n✅ Total questions with images: {len(questions_with_images)}")
    print("="*80)
    
    # Also check if image files actually exist
    print("\n🔍 CHECKING IMAGE FILE EXISTENCE:")
    print("-" * 40)
    
    image_dir = "data/output/excel/images"
    if os.path.exists(image_dir):
        actual_images = os.listdir(image_dir)
        print(f"📁 Found {len(actual_images)} image files in {image_dir}:")
        for img in sorted(actual_images):
            print(f"   ✅ {img}")
    else:
        print(f"❌ Image directory not found: {image_dir}")

if __name__ == "__main__":
    display_questions_with_images()
