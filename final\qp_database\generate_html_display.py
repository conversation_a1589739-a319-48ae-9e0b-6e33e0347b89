import pandas as pd
import os
import re
from datetime import datetime

def generate_html_display():
    """Generate an HTML page to display questions with their correctly mapped images."""
    
    # Path to the Excel file
    excel_path = "data/output/excel/clgdunia_questions_simple.xlsx"
    
    if not os.path.exists(excel_path):
        print("❌ Excel file not found. Please run the main processing first.")
        return
    
    # Load the Excel file
    try:
        df = pd.read_excel(excel_path)
        print(f"📊 Loaded {len(df)} questions from Excel file")
    except Exception as e:
        print(f"❌ Error loading Excel file: {e}")
        return
    
    # Find questions with images for statistics
    questions_with_images = df[df['question'].str.contains(r'\[Image: images/', na=False)]

    # We'll display ALL questions, but highlight which ones have images
    all_questions = df

    # Start building HTML
    html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Questions with Mapped Images</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 3px solid #4CAF50;
        }}
        .header h1 {{
            color: #2c3e50;
            margin: 0;
            font-size: 2.5em;
        }}
        .stats {{
            background-color: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 30px;
            text-align: center;
        }}
        .stats h2 {{
            color: #27ae60;
            margin: 0;
        }}
        .question-card {{
            background-color: #fafafa;
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 25px;
            padding: 20px;
            transition: transform 0.2s;
        }}
        .question-card.has-image {{
            border-left: 5px solid #27ae60;
            background-color: #f8fff8;
        }}
        .question-card.no-image {{
            border-left: 5px solid #e74c3c;
            background-color: #fff8f8;
        }}
        .question-card:hover {{
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }}
        .question-number {{
            background-color: #3498db;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            display: inline-block;
            font-weight: bold;
            margin-bottom: 15px;
        }}
        .question-text {{
            font-size: 1.1em;
            color: #2c3e50;
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }}
        .image-info {{
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
        }}
        .image-path {{
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            color: #e74c3c;
            font-weight: bold;
        }}
        .image-preview {{
            text-align: center;
            margin-top: 15px;
        }}
        .image-preview img {{
            max-width: 100%;
            max-height: 400px;
            border: 2px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .options {{
            margin-top: 15px;
        }}
        .option {{
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 10px;
            margin: 5px 0;
        }}
        .option-label {{
            font-weight: bold;
            color: #8e44ad;
        }}
        .no-images {{
            text-align: center;
            color: #e74c3c;
            font-size: 1.2em;
            padding: 40px;
        }}
        .footer {{
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #7f8c8d;
        }}
        .success-icon {{
            color: #27ae60;
            font-size: 1.2em;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📋 All Questions Display</h1>
            <p>Generated on {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>

        <div class="stats">
            <h2><span class="success-icon">✅</span> Showing all {len(df)} questions | {len(questions_with_images)} have correctly mapped images</h2>
        </div>
"""

    # Display ALL questions
    for index, row in df.iterrows():
        qno = index + 1  # Question number (row index + 1)
        question_text = str(row['question'])

        # Check if this question has an image
        has_image = '[Image: images/' in question_text

        # Extract image path from question text if it exists
        image_match = re.search(r'\[Image: (images/[^\]]+)\]', question_text)
        image_path = image_match.group(1) if image_match else None

        # Clean question text (remove image reference for display)
        clean_question = re.sub(r'\s*\[Image: [^\]]+\]', '', question_text).strip()

        # Check if image file exists
        image_exists = False
        full_image_path = None
        if image_path:
            full_image_path = f"data/output/excel/{image_path}"
            image_exists = os.path.exists(full_image_path)

        # Determine card class based on image presence
        card_class = "has-image" if has_image else "no-image"

        html_content += f"""
        <div class="question-card {card_class}">
            <div class="question-number">Question {qno} {'🖼️' if has_image else '📝'}</div>
            
            <div class="question-text">
                {clean_question}
            </div>
            
            """

        # Add image information only if question has an image
        if has_image and image_path:
            html_content += f"""
            <div class="image-info">
                <strong>🖼️ Image Path:</strong><br>
                <div class="image-path">{image_path}</div>
                <small>{'✅ File exists' if image_exists else '❌ File not found'}</small>
            </div>
            """

            # Add image preview if file exists
            if image_exists:
                html_content += f"""
            <div class="image-preview">
                <img src="{full_image_path}" alt="Question {qno} Image" onerror="this.style.display='none'">
            </div>
                """

        # Add options for all questions
        html_content += """
            <div class="options">
                <strong>📝 Options:</strong>
        """

        for opt_key, opt_letter in [('optiona', 'A'), ('optionb', 'B'), ('optionc', 'C'), ('optiond', 'D')]:
            if opt_key in row and pd.notna(row[opt_key]):
                opt_text = str(row[opt_key])
                html_content += f"""
                <div class="option">
                    <span class="option-label">{opt_letter}.</span> {opt_text}
                </div>
                """

        html_content += """
            </div>
        </div>
        """
    
    # Close HTML
    html_content += f"""
        <div class="footer">
            <p>Generated by Question Paper Image Mapper | Total Questions: {len(df)} | Questions with Images: {len(questions_with_images)}</p>
        </div>
    </div>
</body>
</html>
    """
    
    # Save HTML file
    html_file_path = "questions_with_images.html"
    try:
        with open(html_file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ HTML display page generated: {html_file_path}")
        print(f"🌐 Open the file in your browser to view the questions with images")
        
        # Get absolute path for user
        abs_path = os.path.abspath(html_file_path)
        print(f"📁 Full path: {abs_path}")
        
    except Exception as e:
        print(f"❌ Error generating HTML file: {e}")

if __name__ == "__main__":
    generate_html_display()
